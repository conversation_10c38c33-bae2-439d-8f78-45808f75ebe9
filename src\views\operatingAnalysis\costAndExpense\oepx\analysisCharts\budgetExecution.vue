<template>
  <div class="budgetExecution">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "budgetExecution",
  data() {
    return {
      yData: ["WC16-2", "YC13-10", "LS25-1", "LS17-2", "YC13-1"],
      budgetData: [60, 50, 35, 25, 15], // 同期预算数据
      opexData: [55, 45, 30, 20, 12], // OPEX费用数据
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {
        color: ["#248EFF", "#7262FD", "#FF8C42"], // 配色方案
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12,
          },
          formatter: (params) => {
            // 获取Y轴标签作为标题
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;

            // 过滤重复的数据系列，只保留每个唯一名称的第一个系列
            const uniqueSeries = [];
            const seenNames = new Set();

            params.forEach((param) => {
              if (!seenNames.has(param.seriesName)) {
                seenNames.add(param.seriesName);
                uniqueSeries.push(param);
              }
            });

            // 计算完成率
            const budgetValue = this.budgetData[params[0].dataIndex];
            const opexValue = this.opexData[params[0].dataIndex];
            const completionRate = ((opexValue / budgetValue) * 100).toFixed(2);

            // 遍历过滤后的唯一数据系列
            uniqueSeries.forEach((param) => {
              let unit = "万元";
              let displayValue = Number(param.value).toFixed(2);

              // 如果是完成率，显示百分比格式
              if (param.seriesName === "同期预算完成率") {
                unit = "%";
                displayValue = completionRate;
              }

              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${displayValue}${unit}
              </div>`;
            });

            return result;
          },
        },
        legend: {
          data: ["同期预算", "OPEX费用", "同期预算完成率"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "4%", // 减少左侧空白区域，从15%调整到8%
          right: "4%",
          bottom: "3%",
          top: "15%",
          containLabel: true, // 确保Y轴标签完整显示
        },
        xAxis: [
          {
            type: "value",
            name: "",
            nameTextStyle: {
              color: "#ACC2E2",
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "category",
            data: this.yData,
            inverse: true, // 反转Y轴，使数据从上到下排列
            nameTextStyle: {
              color: "#ACC2E2",
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "同期预算",
            type: "bar",
            data: this.budgetData,
            barHeight: 15,
            itemStyle: {
              color: "#248EFF",
              borderRadius: [0, 8, 8, 0], // 右侧圆角
            },
            label: {
              show: false,
            },
            z: 1,
          },
          {
            name: "OPEX费用",
            type: "bar",
            data: this.opexData,
            barHeight: 10,
            itemStyle: {
              color: "#7262FD",
              borderRadius: [0, 6, 6, 0], // 右侧圆角
            },
            label: {
              show: false,
            },
            z: 2,
          },
        ],
      };
      mychart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.budgetExecution {
  width: 100%;
  .chart-box {
    width: 95%;
    height: 300px;
  }
}
</style>
